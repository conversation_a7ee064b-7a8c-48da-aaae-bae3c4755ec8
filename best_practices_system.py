#!/usr/bin/env python3
"""
Best Practices System pro Matylda
Implementuje Milník 2.4: Učení ze Zkušenosti
Ukládá a využívá poznatky z předchozích projektů
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from dotenv import load_dotenv
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
import logging

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BestPractice(BaseModel):
    """Model pro best practice záznam"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str = Field(..., description="Název best practice")
    category: str = Field(..., description="Kategorie (metodologie, kvalita, komunikace, atd.)")
    description: str = Field(..., description="Popis best practice")
    context: str = Field(..., description="Kontext kdy se aplikuje")
    lesson_learned: str = Field(..., description="Poučení z projektu")
    project_type: str = Field(..., description="Typ projektu (průzkum_trhu, spokojenost, atd.)")
    success_metrics: List[str] = Field(default_factory=list, description="Metriky úspěchu")
    tags: List[str] = Field(default_factory=list, description="Tagy pro vyhledávání")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    updated_at: str = Field(default_factory=lambda: datetime.now().isoformat())
    usage_count: int = Field(default=0, description="Počet použití")
    effectiveness_score: float = Field(default=0.0, description="Skóre efektivity (0-10)")

class ProjectLearning(BaseModel):
    """Model pro poučení z celého projektu"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    project_name: str = Field(..., description="Název projektu")
    project_type: str = Field(..., description="Typ projektu")
    client_feedback: str = Field(..., description="Zpětná vazba od klienta")
    what_worked: List[str] = Field(..., description="Co fungovalo dobře")
    what_failed: List[str] = Field(..., description="Co nefungovalo")
    improvements: List[str] = Field(..., description="Navrhovaná zlepšení")
    quality_score: float = Field(..., description="Celkové skóre kvality (0-10)")
    created_at: str = Field(default_factory=lambda: datetime.now().isoformat())

class MatyldaBestPracticesSystem:
    """Systém pro správu best practices a učení ze zkušeností"""
    
    def __init__(self, project_root: str = None):
        """Inicializace best practices systému"""
        self.project_root = Path(project_root) if project_root else Path(__file__).parent
        self.best_practices_dir = self.project_root / ".taskmaster" / "best_practices"
        self.learnings_dir = self.project_root / ".taskmaster" / "learnings"
        
        # Vytvoření adresářů
        self.best_practices_dir.mkdir(parents=True, exist_ok=True)
        self.learnings_dir.mkdir(parents=True, exist_ok=True)
        
        # Soubory
        self.best_practices_file = self.best_practices_dir / "best_practices.json"
        self.learnings_file = self.learnings_dir / "project_learnings.json"
        
        # Inicializace souborů
        self._initialize_files()
    
    def _initialize_files(self):
        """Inicializace JSON souborů pokud neexistují"""
        if not self.best_practices_file.exists():
            self._save_best_practices([])
        
        if not self.learnings_file.exists():
            self._save_learnings([])
    
    def _load_best_practices(self) -> List[BestPractice]:
        """Načte best practices ze souboru"""
        try:
            with open(self.best_practices_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return [BestPractice(**item) for item in data]
        except Exception as e:
            logger.error(f"Chyba při načítání best practices: {e}")
            return []
    
    def _save_best_practices(self, practices: List[BestPractice]):
        """Uloží best practices do souboru"""
        try:
            data = [practice.dict() for practice in practices]
            with open(self.best_practices_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ Uloženo {len(practices)} best practices")
        except Exception as e:
            logger.error(f"Chyba při ukládání best practices: {e}")
    
    def _load_learnings(self) -> List[ProjectLearning]:
        """Načte project learnings ze souboru"""
        try:
            with open(self.learnings_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return [ProjectLearning(**item) for item in data]
        except Exception as e:
            logger.error(f"Chyba při načítání learnings: {e}")
            return []
    
    def _save_learnings(self, learnings: List[ProjectLearning]):
        """Uloží project learnings do souboru"""
        try:
            data = [learning.dict() for learning in learnings]
            with open(self.learnings_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"✅ Uloženo {len(learnings)} project learnings")
        except Exception as e:
            logger.error(f"Chyba při ukládání learnings: {e}")
    
    def add_best_practice(self, practice: BestPractice) -> str:
        """Přidá novou best practice"""
        practices = self._load_best_practices()
        practices.append(practice)
        self._save_best_practices(practices)
        logger.info(f"✅ Přidána best practice: {practice.title}")
        return practice.id
    
    def add_project_learning(self, learning: ProjectLearning) -> str:
        """Přidá nové poučení z projektu"""
        learnings = self._load_learnings()
        learnings.append(learning)
        self._save_learnings(learnings)
        logger.info(f"✅ Přidáno project learning: {learning.project_name}")
        return learning.id
    
    def search_best_practices(self, query: str, category: str = None, 
                            project_type: str = None, limit: int = 5) -> List[BestPractice]:
        """Vyhledá relevantní best practices"""
        practices = self._load_best_practices()
        
        # Filtrování
        filtered = practices
        
        if category:
            filtered = [p for p in filtered if p.category.lower() == category.lower()]
        
        if project_type:
            filtered = [p for p in filtered if p.project_type.lower() == project_type.lower()]
        
        # Textové vyhledávání
        if query:
            query_lower = query.lower()
            scored_practices = []
            
            for practice in filtered:
                score = 0
                # Skóre podle relevance
                if query_lower in practice.title.lower():
                    score += 10
                if query_lower in practice.description.lower():
                    score += 5
                if query_lower in practice.context.lower():
                    score += 3
                if any(query_lower in tag.lower() for tag in practice.tags):
                    score += 7
                
                if score > 0:
                    scored_practices.append((practice, score))
            
            # Seřazení podle skóre a efektivity
            scored_practices.sort(key=lambda x: (x[1], x[0].effectiveness_score), reverse=True)
            filtered = [p[0] for p in scored_practices[:limit]]
        
        return filtered[:limit]
    
    def get_project_insights(self, project_type: str = None) -> Dict[str, Any]:
        """Získá insights z předchozích projektů"""
        learnings = self._load_learnings()
        
        if project_type:
            learnings = [l for l in learnings if l.project_type.lower() == project_type.lower()]
        
        if not learnings:
            return {"message": "Žádné insights pro tento typ projektu"}
        
        # Analýza
        total_projects = len(learnings)
        avg_quality = sum(l.quality_score for l in learnings) / total_projects
        
        # Nejčastější problémy
        all_failures = []
        for learning in learnings:
            all_failures.extend(learning.what_failed)
        
        # Nejúspěšnější praktiky
        all_successes = []
        for learning in learnings:
            all_successes.extend(learning.what_worked)
        
        return {
            "total_projects": total_projects,
            "average_quality_score": round(avg_quality, 2),
            "common_failures": list(set(all_failures)),
            "successful_practices": list(set(all_successes)),
            "recent_projects": [l.project_name for l in learnings[-3:]]
        }
    
    def update_practice_usage(self, practice_id: str):
        """Aktualizuje počet použití best practice"""
        practices = self._load_best_practices()
        
        for practice in practices:
            if practice.id == practice_id:
                practice.usage_count += 1
                practice.updated_at = datetime.now().isoformat()
                break
        
        self._save_best_practices(practices)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Vrátí statistiky best practices systému"""
        practices = self._load_best_practices()
        learnings = self._load_learnings()
        
        categories = {}
        for practice in practices:
            categories[practice.category] = categories.get(practice.category, 0) + 1
        
        return {
            "total_best_practices": len(practices),
            "total_project_learnings": len(learnings),
            "categories": categories,
            "most_used_practices": sorted(practices, key=lambda x: x.usage_count, reverse=True)[:5],
            "highest_rated_practices": sorted(practices, key=lambda x: x.effectiveness_score, reverse=True)[:5]
        }

# Globální instance
best_practices_system = MatyldaBestPracticesSystem()

class BestPracticesSearchInput(BaseModel):
    """Input schema pro best practices search tool"""
    query: str = Field(..., description="Vyhledávací dotaz")
    category: Optional[str] = Field(None, description="Kategorie (metodologie, kvalita, komunikace)")
    project_type: Optional[str] = Field(None, description="Typ projektu (průzkum_trhu, spokojenost)")

class BestPracticesSearchTool(BaseTool):
    """Nástroj pro vyhledávání best practices"""
    name: str = "best_practices_search"
    description: str = """
    Vyhledá relevantní best practices a poučení z předchozích projektů.
    
    Tento nástroj umožňuje agentům přistupovat k nasbíraným zkušenostem
    a poučením z dříve realizovaných projektů. Použij ho, když potřebuješ:
    - Najít osvědčené postupy pro konkrétní situaci
    - Vyhnout se známým problémům
    - Aplikovat úspěšné strategie z minulosti
    - Získat insights pro konkrétní typ projektu
    
    Příklady použití:
    - "metodologie dotazníkového šetření"
    - "problémy s kvalitou dat"
    - "komunikace s klientem"
    """
    args_schema: type[BaseModel] = BestPracticesSearchInput
    
    def _run(self, query: str, category: str = None, project_type: str = None) -> str:
        """Spustí vyhledávání best practices"""
        practices = best_practices_system.search_best_practices(
            query=query, 
            category=category, 
            project_type=project_type
        )
        
        if not practices:
            return f"❌ Nenalezeny žádné best practices pro dotaz: '{query}'"
        
        # Formátování výsledků
        result = f"🎯 Nalezeno {len(practices)} relevantních best practices pro: '{query}'\n\n"
        
        for i, practice in enumerate(practices, 1):
            result += f"📋 Best Practice {i}: {practice.title}\n"
            result += f"   📂 Kategorie: {practice.category}\n"
            result += f"   🎯 Kontext: {practice.context}\n"
            result += f"   💡 Popis: {practice.description}\n"
            result += f"   🎓 Poučení: {practice.lesson_learned}\n"
            result += f"   ⭐ Efektivita: {practice.effectiveness_score}/10\n"
            result += f"   🔄 Použito: {practice.usage_count}x\n"
            if practice.tags:
                result += f"   🏷️ Tagy: {', '.join(practice.tags)}\n"
            result += "\n---\n\n"
        
        # Aktualizace usage count
        for practice in practices:
            best_practices_system.update_practice_usage(practice.id)
        
        return result

# Instance nástroje pro použití v agentech
best_practices_search = BestPracticesSearchTool()

def get_best_practices_system() -> MatyldaBestPracticesSystem:
    """Vrátí globální instanci best practices systému"""
    return best_practices_system

if __name__ == "__main__":
    # Test best practices systému
    print("🧪 Testování Best Practices systému...")
    
    system = MatyldaBestPracticesSystem()
    
    # Test statistik
    stats = system.get_statistics()
    print(f"📊 Statistiky: {stats}")
    
    print("\n✅ Best Practices systém připraven!")
