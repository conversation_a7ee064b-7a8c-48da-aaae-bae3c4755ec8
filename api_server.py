#!/usr/bin/env python3
"""
FastAPI server pro Matylda - Strategický AI Partner
Implementuje API endpointy pro komunikaci s frontendem
"""

import os
import uuid
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from dotenv import load_dotenv
from interactive_main import MatyldaInteractive

# Načtení konfigurace
load_dotenv()

# Inicializace FastAPI
app = FastAPI(
    title="Matylda API",
    description="API pro strategického AI partnera Matylda",
    version="1.0.0"
)

# Úložiště aktivních session
active_sessions: Dict[str, MatyldaInteractive] = {}

# Pydantic modely pro API
class StartConversationRequest(BaseModel):
    initial_request: str

class StartConversationResponse(BaseModel):
    session_id: str
    question: str
    status: str

class AnswerRequest(BaseModel):
    session_id: str
    answer: str

class AnswerResponse(BaseModel):
    session_id: str
    question: Optional[str] = None
    final_analysis: Optional[Dict[str, Any]] = None
    status: str
    is_complete: bool

class SessionStatusResponse(BaseModel):
    session_id: str
    exists: bool
    summary: Optional[Dict[str, Any]] = None

# API Endpointy

@app.get("/")
async def root():
    """Základní endpoint pro ověření funkčnosti API"""
    return {
        "message": "Matylda API je aktivní",
        "version": "1.0.0",
        "status": "running"
    }

@app.post("/conversation/start", response_model=StartConversationResponse)
async def start_conversation(request: StartConversationRequest):
    """
    Zahájení nové konverzace s Matylda systémem
    """
    try:
        # Vytvoření nové session
        session_id = str(uuid.uuid4())
        matylda = MatyldaInteractive()
        
        # Zahájení konverzace
        question = matylda.start_conversation(request.initial_request)
        
        # Uložení session
        active_sessions[session_id] = matylda
        
        return StartConversationResponse(
            session_id=session_id,
            question=question,
            status="active"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při zahájení konverzace: {str(e)}")

@app.post("/conversation/answer", response_model=AnswerResponse)
async def process_answer(request: AnswerRequest):
    """
    Zpracování odpovědi uživatele a získání další otázky nebo finálního výsledku
    """
    try:
        # Ověření existence session
        if request.session_id not in active_sessions:
            raise HTTPException(status_code=404, detail="Session nenalezena")
        
        matylda = active_sessions[request.session_id]
        
        # Zpracování odpovědi
        result = matylda.process_answer(request.answer)
        
        # Kontrola, zda je konverzace dokončena
        if isinstance(result, dict) and 'projekt_cil' in result:
            # Konverzace je dokončena
            return AnswerResponse(
                session_id=request.session_id,
                question=None,
                final_analysis=result,
                status="completed",
                is_complete=True
            )
        else:
            # Pokračování v konverzaci
            return AnswerResponse(
                session_id=request.session_id,
                question=result,
                final_analysis=None,
                status="active",
                is_complete=False
            )
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování odpovědi: {str(e)}")

@app.get("/conversation/{session_id}/status", response_model=SessionStatusResponse)
async def get_session_status(session_id: str):
    """
    Získání stavu konkrétní session
    """
    try:
        if session_id not in active_sessions:
            return SessionStatusResponse(
                session_id=session_id,
                exists=False,
                summary=None
            )
        
        matylda = active_sessions[session_id]
        summary = matylda.get_session_summary()
        
        return SessionStatusResponse(
            session_id=session_id,
            exists=True,
            summary=summary
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání stavu session: {str(e)}")

@app.delete("/conversation/{session_id}")
async def delete_session(session_id: str):
    """
    Smazání session z paměti
    """
    try:
        if session_id in active_sessions:
            del active_sessions[session_id]
            return {"message": f"Session {session_id} byla smazána", "status": "deleted"}
        else:
            raise HTTPException(status_code=404, detail="Session nenalezena")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při mazání session: {str(e)}")

@app.get("/sessions")
async def list_active_sessions():
    """
    Seznam všech aktivních sessions
    """
    try:
        sessions_info = []
        for session_id, matylda in active_sessions.items():
            summary = matylda.get_session_summary()
            sessions_info.append({
                "session_id": session_id,
                "summary": summary
            })
        
        return {
            "active_sessions_count": len(active_sessions),
            "sessions": sessions_info
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při získávání seznamu sessions: {str(e)}")

@app.get("/health")
async def health_check():
    """
    Health check endpoint pro monitoring
    """
    return {
        "status": "healthy",
        "active_sessions": len(active_sessions),
        "api_version": "1.0.0"
    }

# Spuštění serveru
if __name__ == "__main__":
    import uvicorn
    
    port = 8001
    print("🚀 Spouštím Matylda API server...")
    print(f"📖 Dokumentace dostupná na: http://localhost:{port}/docs")
    print(f"🔍 Health check: http://localhost:{port}/health")

    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=port,
        reload=True
    )
