#!/usr/bin/env python3
"""
Test script pro ověření funkčnosti Matylda API
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_api():
    """Test základní funkčnosti API"""
    print("🧪 Testování Matylda API...")
    
    # Test 1: Health check
    print("\n1️⃣ Test health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check OK")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Zahájení konverzace
    print("\n2️⃣ Test zahájení konverzace...")
    try:
        start_data = {
            "initial_request": "Potřebuji udělat průzkum k rekonstrukci dětského hřiště."
        }
        response = requests.post(f"{BASE_URL}/conversation/start", json=start_data)
        
        if response.status_code == 200:
            start_result = response.json()
            session_id = start_result["session_id"]
            question = start_result["question"]
            print("✅ Konverzace zahájena")
            print(f"   Session ID: {session_id}")
            print(f"   První otázka: {question}")
        else:
            print(f"❌ Zahájení konverzace selhalo: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chyba při zahájení konverzace: {e}")
        return False
    
    # Test 3: Odpovědi na otázky
    print("\n3️⃣ Test odpovědí...")
    answers = [
        "Chceme, aby hřiště bylo bezpečnější a zábavnější pro děti různých věkových kategorií.",
        "Máme rozpočet kolem 500 000 Kč a chtěli bychom to stihnout do léta.",
        "Určitě bychom chtěli zapojit rodiče i děti do rozhodování o tom, co přidat."
    ]
    
    for i, answer in enumerate(answers):
        print(f"\n   Odpověď {i+1}: {answer}")
        try:
            answer_data = {
                "session_id": session_id,
                "answer": answer
            }
            response = requests.post(f"{BASE_URL}/conversation/answer", json=answer_data)
            
            if response.status_code == 200:
                result = response.json()
                if result["is_complete"]:
                    print("✅ Konverzace dokončena!")
                    print(f"   Finální analýza: {json.dumps(result['final_analysis'], indent=2, ensure_ascii=False)}")
                    break
                else:
                    print(f"✅ Další otázka: {result['question']}")
            else:
                print(f"❌ Chyba při zpracování odpovědi: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Chyba při odpovědi: {e}")
            return False
    
    # Test 4: Status session
    print("\n4️⃣ Test status session...")
    try:
        response = requests.get(f"{BASE_URL}/conversation/{session_id}/status")
        if response.status_code == 200:
            status = response.json()
            print("✅ Status session OK")
            print(f"   Summary: {json.dumps(status['summary'], indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ Status session failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chyba při získávání statusu: {e}")
    
    # Test 5: Seznam aktivních sessions
    print("\n5️⃣ Test seznamu aktivních sessions...")
    try:
        response = requests.get(f"{BASE_URL}/sessions")
        if response.status_code == 200:
            sessions = response.json()
            print("✅ Seznam sessions OK")
            print(f"   Aktivní sessions: {sessions['active_sessions_count']}")
        else:
            print(f"❌ Seznam sessions failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chyba při získávání seznamu sessions: {e}")
    
    print("\n🎉 Test API dokončen!")
    return True

if __name__ == "__main__":
    # Počkáme chvilku, aby se server spustil
    print("⏳ Čekám na spuštění serveru...")
    time.sleep(2)
    
    test_api()
