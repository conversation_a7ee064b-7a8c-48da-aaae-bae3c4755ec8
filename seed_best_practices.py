#!/usr/bin/env python3
"""
Seed script pro naplnění best practices systému počátečními daty
"""

from best_practices_system import BestPractice, ProjectLearning, get_best_practices_system

def seed_best_practices():
    """Naplní systém počátečními best practices"""
    
    system = get_best_practices_system()
    
    # Best practices pro metodologii
    practices = [
        BestPractice(
            title="Stratifikovaný vzorek pro reprezentativnost",
            category="metodologie",
            description="Použití stratifikovaného vzorkování podle demografických ukazatelů zajišťuje reprezentativnost výsledků.",
            context="Při kvantitativních výzkumech s potřebou generalizace na celou populaci",
            lesson_learned="Prostý náhodný vzorek často nevede k reprezentativním výsledkům, zejména u menších vzorků.",
            project_type="průzkum_trhu",
            success_metrics=["reprezentativnost vzorku", "statistická významnost", "nízký sampling bias"],
            tags=["vzorkování", "reprezentativnost", "kvantitativní", "statistika"],
            effectiveness_score=8.5
        ),
        
        BestPractice(
            title="Attention checks proti nekvalitním respondentům",
            category="kvalita_dat",
            description="Zahrnutí kontrolních otázek do dotazníku pro identifikaci nepozorných respondentů.",
            context="Online dotazníkové šetření s rizikem straight-lining nebo náhodných odpovědí",
            lesson_learned="Bez attention checks může být až 15-20% dat nekvalitních a zkreslit výsledky.",
            project_type="spokojenost",
            success_metrics=["kvalita dat", "konzistence odpovědí", "validita výsledků"],
            tags=["kvalita", "dotazník", "validace", "respondenti"],
            effectiveness_score=9.2
        ),
        
        BestPractice(
            title="Pilotní testování dotazníku",
            category="metodologie",
            description="Vždy provést pilotní test dotazníku na malém vzorku před hlavním sběrem dat.",
            context="Před spuštěním jakéhokoliv dotazníkového šetření",
            lesson_learned="Pilotní test odhalí nejasnosti v otázkách a technické problémy, které by jinak znehodnotily data.",
            project_type="všechny",
            success_metrics=["srozumitelnost otázek", "technická funkčnost", "doba vyplnění"],
            tags=["pilotáž", "testování", "dotazník", "validace"],
            effectiveness_score=8.8
        ),
        
        BestPractice(
            title="Strukturované JSON výstupy pro konzistenci",
            category="komunikace",
            description="Používání strukturovaných JSON formátů pro všechny analytické výstupy zajišťuje konzistenci a snadné zpracování.",
            context="Při předávání výsledků analýz mezi agenty nebo klientovi",
            lesson_learned="Nestrukturované výstupy vedou k nejasnostem a chybám v interpretaci.",
            project_type="všechny",
            success_metrics=["konzistence výstupů", "snadnost zpracování", "jasnost komunikace"],
            tags=["výstupy", "struktura", "JSON", "konzistence"],
            effectiveness_score=7.9
        ),
        
        BestPractice(
            title="Kombinace kvantitativních a kvalitativních metod",
            category="metodologie",
            description="Kombinace číselných dat s otevřenými otázkami poskytuje hlubší vhled do problematiky.",
            context="Komplexní výzkumné projekty vyžadující pochopení 'proč' za čísly",
            lesson_learned="Pouze kvantitativní data často neodpoví na otázku proč se něco děje.",
            project_type="spokojenost",
            success_metrics=["hloubka insights", "pochopení příčin", "akční doporučení"],
            tags=["mixed-methods", "kvalitativní", "kvantitativní", "insights"],
            effectiveness_score=8.3
        ),
        
        BestPractice(
            title="Pravidelné kontroly kvality během sběru",
            category="kvalita_dat",
            description="Monitoring kvality dat v reálném čase během sběru umožňuje rychlé zásahy.",
            context="Dlouhodobé sběry dat trvající více než týden",
            lesson_learned="Problémy s kvalitou dat se často projeví až po několika dnech sběru.",
            project_type="všechny",
            success_metrics=["včasná detekce problémů", "kvalita dat", "efektivita sběru"],
            tags=["monitoring", "kvalita", "real-time", "kontrola"],
            effectiveness_score=8.1
        ),
        
        BestPractice(
            title="Jasná komunikace očekávání s klientem",
            category="komunikace",
            description="Na začátku projektu vždy jasně definovat očekávání, deliverables a časový harmonogram.",
            context="Zahájení každého nového projektu s klientem",
            lesson_learned="Nejasná očekávání vedou k nespokojenosti klienta i při kvalitně odvedené práci.",
            project_type="všechny",
            success_metrics=["spokojenost klienta", "jasnost požadavků", "dodržení termínů"],
            tags=["klient", "očekávání", "komunikace", "projekt"],
            effectiveness_score=9.0
        ),
        
        BestPractice(
            title="Backup plán pro nízkou response rate",
            category="metodologie",
            description="Vždy mít připravený plán B pro případ nízké návratnosti dotazníků.",
            context="Online výzkumy s nejistou response rate",
            lesson_learned="Response rate může být výrazně nižší než očekáváno, zejména u specifických cílových skupin.",
            project_type="průzkum_trhu",
            success_metrics=["dosažení cílového vzorku", "reprezentativnost", "dodržení termínů"],
            tags=["response-rate", "backup", "plánování", "rizika"],
            effectiveness_score=7.6
        )
    ]
    
    # Přidání best practices
    for practice in practices:
        system.add_best_practice(practice)
    
    print(f"✅ Přidáno {len(practices)} best practices")

def seed_project_learnings():
    """Naplní systém počátečními project learnings"""
    
    system = get_best_practices_system()
    
    learnings = [
        ProjectLearning(
            project_name="Průzkum spokojenosti zaměstnanců TechCorp",
            project_type="spokojenost",
            client_feedback="Výborné výsledky, jasné doporučení, rychlé dodání",
            what_worked=[
                "Stratifikovaný vzorek podle oddělení",
                "Kombinace kvantitativních a kvalitativních otázek",
                "Pravidelné updates pro klienta",
                "Jasná vizualizace výsledků"
            ],
            what_failed=[
                "Nízká response rate v IT oddělení",
                "Některé otázky byly nejasné",
                "Chyběly attention checks"
            ],
            improvements=[
                "Přidat attention checks do dotazníku",
                "Lepší komunikace s IT oddělením",
                "Pilotní test na větším vzorku"
            ],
            quality_score=8.2
        ),
        
        ProjectLearning(
            project_name="Průzkum trhu pro EcoPackaging",
            project_type="průzkum_trhu",
            client_feedback="Dobré insights, ale pomalé dodání analýzy",
            what_worked=[
                "Kvalitní segmentace trhu",
                "Dobré pokrytí konkurence",
                "Strukturované JSON výstupy"
            ],
            what_failed=[
                "Analýza dat trvala příliš dlouho",
                "Chyběly některé demografické údaje",
                "Nedostatečná komunikace s klientem"
            ],
            improvements=[
                "Více času na analýzu dat v harmonogramu",
                "Lepší sběr demografických dat",
                "Častější komunikace s klientem"
            ],
            quality_score=7.1
        ),
        
        ProjectLearning(
            project_name="Spokojenost občanů s městskými službami",
            project_type="spokojenost",
            client_feedback="Excelentní práce, velmi užitečná doporučení",
            what_worked=[
                "Výborná response rate díky lokální kampani",
                "Kvalitní attention checks",
                "Detailní analýza podle městských částí",
                "Akční doporučení s prioritami"
            ],
            what_failed=[
                "Technické problémy s dotazníkem první den",
                "Některé otázky byly příliš dlouhé"
            ],
            improvements=[
                "Důkladnější technické testování",
                "Kratší a jasnější formulace otázek"
            ],
            quality_score=9.1
        )
    ]
    
    # Přidání project learnings
    for learning in learnings:
        system.add_project_learning(learning)
    
    print(f"✅ Přidáno {len(learnings)} project learnings")

if __name__ == "__main__":
    print("🌱 Naplňování Best Practices systému...")
    
    seed_best_practices()
    seed_project_learnings()
    
    # Zobrazení statistik
    system = get_best_practices_system()
    stats = system.get_statistics()
    
    print(f"\n📊 Statistiky po naplnění:")
    print(f"   Best practices: {stats['total_best_practices']}")
    print(f"   Project learnings: {stats['total_project_learnings']}")
    print(f"   Kategorie: {list(stats['categories'].keys())}")
    
    print("\n🎉 Best Practices systém je připraven!")
