#!/usr/bin/env python3
"""
Test rozšířené posádky s Best Practices systémem
Implementuje Milník 2.4: Učen<PERSON> ze Zkušenosti
"""

import os
from dotenv import load_dotenv
from config_loader import get_config_loader
from rag_system import get_rag_system
from best_practices_system import get_best_practices_system

# Načtení konfigurace
load_dotenv()

def test_best_practices_integration():
    """Test integrace best practices do rozšířené posádky"""
    
    print("🎭 === TEST BEST PRACTICES INTEGRACE ===")
    print("Testování Milníku 2.4: Učení ze Zkušenosti\n")
    
    # Inicializace systémů
    config_loader = get_config_loader()
    rag_system = get_rag_system()
    bp_system = get_best_practices_system()
    
    print(f"🔍 RAG systém: {'✅ dostupný' if rag_system.is_available() else '❌ nedostupný'}")
    print(f"🎓 Best Practices: ✅ dostupný")
    
    # Zobrazení statistik best practices
    stats = bp_system.get_statistics()
    print(f"\n📊 Best Practices statistiky:")
    print(f"   📋 Best practices: {stats['total_best_practices']}")
    print(f"   📚 Project learnings: {stats['total_project_learnings']}")
    print(f"   📂 Kategorie: {list(stats['categories'].keys())}")
    
    # Test vstupní požadavek
    demo_request = "Potřebuji udělat průzkum spokojenosti zákazníků s naším e-shopem."
    
    print(f"\n🚀 Spouštím rozšířenou posádku s best practices...")
    print(f"📝 Vstupní požadavek: {demo_request}")
    
    try:
        # Vytvoření rozšířené posádky
        crew = config_loader.create_crew(
            "onboarding_crew_extended", 
            initial_request=demo_request
        )
        
        if not crew:
            print("❌ Nepodařilo se vytvořit rozšířenou posádku")
            return False
        
        print(f"✅ Rozšířená posádka vytvořena:")
        print(f"   👥 Agenti: {len(crew.agents)}")
        print(f"   📋 Úkoly: {len(crew.tasks)}")
        
        # Zobrazení agentů a jejich nástrojů
        print("\n🤖 Agenti v posádce:")
        for i, agent in enumerate(crew.agents, 1):
            print(f"   {i}. {agent.role}")
            print(f"      🔧 Nástroje: {len(agent.tools)} ({[tool.name for tool in agent.tools]})")
        
        # Spuštění posádky
        print("\n🎬 Spouštím rozšířenou posádku s best practices...")
        print("=" * 60)
        
        result = crew.kickoff()
        
        print("\n" + "=" * 60)
        print("## 🎯 FINÁLNÍ VÝSLEDEK S BEST PRACTICES:")
        print("=" * 60)
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při spuštění rozšířené posádky: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_best_practices_search():
    """Test vyhledávání best practices"""
    
    print("\n\n🔍 === TEST VYHLEDÁVÁNÍ BEST PRACTICES ===")
    
    bp_system = get_best_practices_system()
    
    # Test různých vyhledávání
    test_queries = [
        {"query": "dotazník", "category": None, "project_type": None},
        {"query": "kvalita dat", "category": "kvalita_dat", "project_type": None},
        {"query": "komunikace", "category": None, "project_type": "spokojenost"},
        {"query": "metodologie", "category": "metodologie", "project_type": "průzkum_trhu"}
    ]
    
    for test in test_queries:
        print(f"\n🔍 Vyhledávání: '{test['query']}'")
        if test['category']:
            print(f"   📂 Kategorie: {test['category']}")
        if test['project_type']:
            print(f"   🎯 Typ projektu: {test['project_type']}")
        
        practices = bp_system.search_best_practices(
            query=test['query'],
            category=test['category'],
            project_type=test['project_type'],
            limit=3
        )
        
        if practices:
            print(f"   ✅ Nalezeno {len(practices)} best practices:")
            for practice in practices:
                print(f"      • {practice.title} (⭐ {practice.effectiveness_score}/10)")
        else:
            print("   ❌ Žádné best practices nenalezeny")

def test_project_insights():
    """Test získávání insights z projektů"""
    
    print("\n\n📊 === TEST PROJECT INSIGHTS ===")
    
    bp_system = get_best_practices_system()
    
    # Test insights pro různé typy projektů
    project_types = ["spokojenost", "průzkum_trhu", "všechny"]
    
    for project_type in project_types:
        print(f"\n📈 Insights pro typ: {project_type}")
        
        if project_type == "všechny":
            insights = bp_system.get_project_insights()
        else:
            insights = bp_system.get_project_insights(project_type)
        
        if "message" in insights:
            print(f"   ℹ️ {insights['message']}")
        else:
            print(f"   📊 Celkem projektů: {insights['total_projects']}")
            print(f"   ⭐ Průměrná kvalita: {insights['average_quality_score']}/10")
            print(f"   ✅ Úspěšné praktiky: {len(insights['successful_practices'])}")
            print(f"   ❌ Časté problémy: {len(insights['common_failures'])}")

def show_best_practices_summary():
    """Zobrazí shrnutí best practices systému"""
    
    print("\n\n📖 === SHRNUTÍ BEST PRACTICES SYSTÉMU ===")
    
    bp_system = get_best_practices_system()
    stats = bp_system.get_statistics()
    
    print(f"\n📊 Celkové statistiky:")
    print(f"   📋 Best practices: {stats['total_best_practices']}")
    print(f"   📚 Project learnings: {stats['total_project_learnings']}")
    
    print(f"\n📂 Kategorie best practices:")
    for category, count in stats['categories'].items():
        print(f"   • {category}: {count}")
    
    print(f"\n⭐ Top 3 nejefektivnější best practices:")
    for i, practice in enumerate(stats['highest_rated_practices'][:3], 1):
        print(f"   {i}. {practice.title} ({practice.effectiveness_score}/10)")
    
    print(f"\n🔄 Top 3 nejpoužívanější best practices:")
    for i, practice in enumerate(stats['most_used_practices'][:3], 1):
        print(f"   {i}. {practice.title} ({practice.usage_count}x)")

if __name__ == "__main__":
    print("🧪 Testování Milníku 2.4: Učení ze Zkušenosti")
    print("=" * 70)
    
    # Test vyhledávání best practices
    test_best_practices_search()
    
    # Test project insights
    test_project_insights()
    
    # Shrnutí systému
    show_best_practices_summary()
    
    # Hlavní test rozšířené posádky s best practices
    print("\n" + "=" * 70)
    success = test_best_practices_integration()
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 Milník 2.4 úspěšně implementován!")
        print("✅ Best Practices systém je plně funkční")
        print("✅ Agenti mají přístup k osvědčeným postupům")
        print("✅ Systém učení ze zkušeností je aktivní")
        print("✅ Rozšířená posádka využívá best practices")
    else:
        print("❌ Test rozšířené posádky s best practices selhal")
    
    print("\n💡 Tip: Best practices se automaticky aktualizují při použití")
    print("📚 Systém se učí z každého dokončeného projektu")
