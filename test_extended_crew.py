#!/usr/bin/env python3
"""
Test script pro rozšířenou posádku s agenty SurveyArchitect a QualitySupervisor
Implementuje Milník 2.3: Rozšíření Týmu
"""

import os
from dotenv import load_dotenv
from config_loader import get_config_loader
from rag_system import get_rag_system

# Načtení konfigurace
load_dotenv()

def test_extended_crew():
    """Test rozšířené posádky s 4 agenty"""
    
    print("🎭 === TEST ROZŠÍŘENÉ POSÁDKY ===")
    print("Testování Milníku 2.3: Rozšíření Týmu\n")
    
    # Inicializace
    config_loader = get_config_loader()
    rag_system = get_rag_system()
    
    print(f"🔍 RAG systém: {'✅ dostupný' if rag_system.is_available() else '❌ nedostupný'}")
    
    # Zobrazení dostupných konfigurací
    print("\n📋 Dostupné konfigurace:")
    configs = config_loader.list_available_configs()
    for config_type, items in configs.items():
        print(f"  {config_type}: {items}")
    
    # Test vstupní požadavek
    demo_request = os.getenv("DEMO_INITIAL_REQUEST", "Potřebuji udělat průzkum spokojenosti zaměstnanců v naší firmě.")
    
    print(f"\n🚀 Spouštím rozšířenou posádku...")
    print(f"📝 Vstupní požadavek: {demo_request}")
    
    try:
        # Vytvoření rozšířené posádky
        crew = config_loader.create_crew(
            "onboarding_crew_extended", 
            initial_request=demo_request
        )
        
        if not crew:
            print("❌ Nepodařilo se vytvořit rozšířenou posádku")
            return
        
        print(f"✅ Rozšířená posádka vytvořena:")
        print(f"   👥 Agenti: {len(crew.agents)}")
        print(f"   📋 Úkoly: {len(crew.tasks)}")
        
        # Zobrazení agentů
        print("\n🤖 Agenti v posádce:")
        for i, agent in enumerate(crew.agents, 1):
            print(f"   {i}. {agent.role}")
            print(f"      Nástroje: {len(agent.tools)}")
        
        # Spuštění posádky
        print("\n🎬 Spouštím rozšířenou posádku...")
        print("=" * 50)
        
        result = crew.kickoff()
        
        print("\n" + "=" * 50)
        print("## 🎯 FINÁLNÍ VÝSLEDEK ROZŠÍŘENÉ POSÁDKY:")
        print("=" * 50)
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při spuštění rozšířené posádky: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specialized_crews():
    """Test specializovaných posádek"""
    
    print("\n\n🎯 === TEST SPECIALIZOVANÝCH POSÁDEK ===")
    
    config_loader = get_config_loader()
    
    # Test různých typů projektů
    test_scenarios = [
        {
            "crew_id": "market_research_crew",
            "request": "Potřebuji udělat průzkum trhu pro nový produkt v oblasti ekologických obalů.",
            "name": "Průzkum Trhu"
        },
        {
            "crew_id": "satisfaction_survey_crew", 
            "request": "Chceme změřit spokojenost našich zákazníků s poskytovanými službami.",
            "name": "Průzkum Spokojenosti"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🔍 Test: {scenario['name']}")
        print(f"📝 Požadavek: {scenario['request']}")
        
        try:
            crew = config_loader.create_crew(
                scenario["crew_id"],
                initial_request=scenario["request"]
            )
            
            if crew:
                print(f"✅ Posádka '{scenario['name']}' vytvořena s {len(crew.agents)} agenty")
                
                # Krátký test - pouze vytvoření, ne spuštění (kvůli času)
                print(f"   Agenti: {[agent.role[:30] + '...' for agent in crew.agents]}")
            else:
                print(f"❌ Nepodařilo se vytvořit posádku '{scenario['name']}'")
                
        except Exception as e:
            print(f"❌ Chyba při testování '{scenario['name']}': {e}")

def test_individual_agents():
    """Test jednotlivých nových agentů"""
    
    print("\n\n🤖 === TEST JEDNOTLIVÝCH AGENTŮ ===")
    
    config_loader = get_config_loader()
    
    # Test SurveyArchitect
    print("\n📐 Test SurveyArchitect:")
    architect = config_loader.create_agent("survey_architect_v1")
    if architect:
        print(f"✅ SurveyArchitect vytvořen: {architect.role}")
        print(f"   Nástroje: {len(architect.tools)}")
        print(f"   Cíl: {architect.goal[:80]}...")
    else:
        print("❌ Nepodařilo se vytvořit SurveyArchitect")
    
    # Test QualitySupervisor
    print("\n🔍 Test QualitySupervisor:")
    supervisor = config_loader.create_agent("quality_supervisor_v1")
    if supervisor:
        print(f"✅ QualitySupervisor vytvořen: {supervisor.role}")
        print(f"   Nástroje: {len(supervisor.tools)}")
        print(f"   Cíl: {supervisor.goal[:80]}...")
    else:
        print("❌ Nepodařilo se vytvořit QualitySupervisor")

def show_extended_config():
    """Zobrazí rozšířenou konfiguraci"""
    
    print("\n\n📖 === ROZŠÍŘENÁ KONFIGURACE ===")
    
    config_loader = get_config_loader()
    
    # Aktivní agenti
    print("\n🤖 AKTIVNÍ AGENTI:")
    agents_config = config_loader.load_agents_config()
    for agent_id, config in agents_config.items():
        enabled = config.get('enabled', True)
        if enabled:
            print(f"  ✅ {agent_id}: {config['role']}")
            if config.get('tools'):
                print(f"     🔧 Nástroje: {', '.join(config['tools'])}")
    
    # Aktivní posádky
    print("\n👥 AKTIVNÍ POSÁDKY:")
    crews_config = config_loader.load_crews_config()
    for crew_id, config in crews_config.items():
        enabled = config.get('enabled', True)
        if enabled:
            print(f"  ✅ {crew_id}: {config['name']}")
            print(f"     👥 Agenti ({len(config['agents'])}): {', '.join(config['agents'])}")
            print(f"     📋 Úkoly ({len(config['tasks'])}): {', '.join(config['tasks'])}")

if __name__ == "__main__":
    print("🧪 Testování Milníku 2.3: Rozšíření Týmu")
    print("=" * 60)
    
    # Test jednotlivých agentů
    test_individual_agents()
    
    # Zobrazení konfigurace
    show_extended_config()
    
    # Test specializovaných posádek (pouze vytvoření)
    test_specialized_crews()
    
    # Hlavní test rozšířené posádky
    print("\n" + "=" * 60)
    success = test_extended_crew()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Milník 2.3 úspěšně implementován!")
        print("✅ SurveyArchitect a QualitySupervisor jsou aktivní")
        print("✅ Rozšířená posádka funguje správně")
        print("✅ Specializované posádky jsou připraveny")
    else:
        print("❌ Test rozšířené posádky selhal")
    
    print("\n💡 Tip: Editujte YAML soubory pro úpravu chování agentů")
