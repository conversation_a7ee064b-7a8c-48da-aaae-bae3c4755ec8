#!/usr/bin/env python3
"""
Nástroj pro ukládání poznatků z dokončených projektů
Implementuje Milník 2.4: Učení ze Zkušenosti
"""

from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from best_practices_system import BestPractice, ProjectLearning, get_best_practices_system
from typing import List, Optional
import json

class SaveProjectLearningInput(BaseModel):
    """Input schema pro uložení poznatků z projektu"""
    project_name: str = Field(..., description="Název projektu")
    project_type: str = Field(..., description="Typ projektu (průzkum_trhu, spokojenost, atd.)")
    client_feedback: str = Field(..., description="Zpětná vazba od klienta")
    what_worked: List[str] = Field(..., description="Co fungovalo dobře")
    what_failed: List[str] = Field(..., description="Co nefungovalo")
    improvements: List[str] = Field(..., description="Navrhovaná zlepšení")
    quality_score: float = Field(..., description="Celkové skóre kvality (0-10)")

class SaveBestPracticeInput(BaseModel):
    """Input schema pro uložení best practice"""
    title: str = Field(..., description="Název best practice")
    category: str = Field(..., description="Kategorie (metodologie, kvalita, komunikace)")
    description: str = Field(..., description="Popis best practice")
    context: str = Field(..., description="Kontext kdy se aplikuje")
    lesson_learned: str = Field(..., description="Poučení z projektu")
    project_type: str = Field(..., description="Typ projektu")
    effectiveness_score: float = Field(default=8.0, description="Skóre efektivity (0-10)")
    tags: List[str] = Field(default_factory=list, description="Tagy pro vyhledávání")

class SaveProjectLearningTool(BaseTool):
    """Nástroj pro uložení poznatků z dokončeného projektu"""
    name: str = "save_project_learning"
    description: str = """
    Uloží poznatky a poučení z dokončeného projektu do systému učení.
    
    Tento nástroj umožňuje agentům ukládat zkušenosti z projektů pro budoucí využití.
    Použij ho na konci projektu pro zachycení:
    - Co fungovalo dobře
    - Co nefungovalo
    - Zpětné vazby od klienta
    - Navrhovaná zlepšení
    - Celkové hodnocení kvality
    
    Tyto informace budou využity pro zlepšení budoucích projektů.
    """
    args_schema: type[BaseModel] = SaveProjectLearningInput
    
    def _run(self, project_name: str, project_type: str, client_feedback: str,
             what_worked: List[str], what_failed: List[str], improvements: List[str],
             quality_score: float) -> str:
        """Uloží poznatky z projektu"""
        
        system = get_best_practices_system()
        
        learning = ProjectLearning(
            project_name=project_name,
            project_type=project_type,
            client_feedback=client_feedback,
            what_worked=what_worked,
            what_failed=what_failed,
            improvements=improvements,
            quality_score=quality_score
        )
        
        learning_id = system.add_project_learning(learning)
        
        return f"""✅ Poznatky z projektu úspěšně uloženy!

📊 Projekt: {project_name}
🎯 Typ: {project_type}
⭐ Kvalita: {quality_score}/10
💬 Zpětná vazba: {client_feedback}

✅ Co fungovalo ({len(what_worked)} bodů):
{chr(10).join(f"  • {item}" for item in what_worked)}

❌ Co nefungovalo ({len(what_failed)} bodů):
{chr(10).join(f"  • {item}" for item in what_failed)}

🔧 Zlepšení ({len(improvements)} bodů):
{chr(10).join(f"  • {item}" for item in improvements)}

🆔 ID záznamu: {learning_id}

Tyto poznatky budou využity pro zlepšení budoucích projektů podobného typu."""

class SaveBestPracticeTool(BaseTool):
    """Nástroj pro uložení nové best practice"""
    name: str = "save_best_practice"
    description: str = """
    Uloží novou best practice do systému učení.
    
    Tento nástroj umožňuje agentům ukládat osvědčené postupy pro budoucí využití.
    Použij ho když objevíš efektivní postup, který by měl být zachován:
    - Metodologické postupy
    - Techniky kontroly kvality
    - Komunikační strategie
    - Řešení problémů
    
    Best practice bude dostupná pro všechny budoucí projekty.
    """
    args_schema: type[BaseModel] = SaveBestPracticeInput
    
    def _run(self, title: str, category: str, description: str, context: str,
             lesson_learned: str, project_type: str, effectiveness_score: float = 8.0,
             tags: List[str] = None) -> str:
        """Uloží novou best practice"""
        
        if tags is None:
            tags = []
        
        system = get_best_practices_system()
        
        practice = BestPractice(
            title=title,
            category=category,
            description=description,
            context=context,
            lesson_learned=lesson_learned,
            project_type=project_type,
            effectiveness_score=effectiveness_score,
            tags=tags
        )
        
        practice_id = system.add_best_practice(practice)
        
        return f"""✅ Best practice úspěšně uložena!

📋 Název: {title}
📂 Kategorie: {category}
🎯 Typ projektu: {project_type}
⭐ Efektivita: {effectiveness_score}/10

💡 Popis: {description}

🎯 Kontext: {context}

🎓 Poučení: {lesson_learned}

🏷️ Tagy: {', '.join(tags) if tags else 'Žádné'}

🆔 ID záznamu: {practice_id}

Tato best practice je nyní dostupná pro všechny budoucí projekty."""

# Instance nástrojů
save_project_learning = SaveProjectLearningTool()
save_best_practice = SaveBestPracticeTool()

if __name__ == "__main__":
    # Test nástrojů
    print("🧪 Testování Project Learning nástrojů...")
    
    # Test uložení project learning
    result = save_project_learning._run(
        project_name="Test projekt",
        project_type="spokojenost",
        client_feedback="Velmi dobré výsledky",
        what_worked=["Dobrá komunikace", "Kvalitní analýza"],
        what_failed=["Pomalý sběr dat"],
        improvements=["Rychlejší metodologie"],
        quality_score=8.5
    )
    print(result)
    
    print("\n" + "="*50)
    
    # Test uložení best practice
    result = save_best_practice._run(
        title="Test best practice",
        category="metodologie",
        description="Testovací popis",
        context="Testovací kontext",
        lesson_learned="Testovací poučení",
        project_type="všechny",
        effectiveness_score=9.0,
        tags=["test", "metodologie"]
    )
    print(result)
    
    print("\n✅ Nástroje fungují správně!")
