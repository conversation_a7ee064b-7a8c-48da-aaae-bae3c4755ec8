#!/usr/bin/env python3
"""
Test script pro ověření základní struktury projektu Matylda
Testuje inicializaci agentů a úkolů bez volání OpenAI API
"""

import os
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process

def test_basic_structure():
    """Test základní struktury bez volání LLM"""
    print("🧪 Testování základní struktury projektu Matylda...")
    
    # Načtení konfigurace
    load_dotenv()
    print("✅ Konfigurace načtena")
    
    # Test definice agentů
    try:
        client_onboarder = Agent(
            role='Expert na vedení klientských rozhovorů a sběr zadání',
            goal='Vést efektivní a empatický dialog s klientem, abys pochopil jeho skutečné potřeby a cíle pro výzkumný projekt.',
            backstory=(
                "Jsi zkušený konzultant s praxí z top poradenských firem. "
                "<PERSON><PERSON><PERSON> kl<PERSON>t cí<PERSON>, <PERSON><PERSON><PERSON> j<PERSON> pod povrch, a aktivně naslouch<PERSON>š, "
                "abys odhalil klíčové informace skryté za klientovými slovy."
            ),
            verbose=True,
            allow_delegation=False,
        )
        print("✅ Agent ClientOnboarder vytvořen")
        
        brief_analyst = Agent(
            role='Specialista na analýzu a validaci klientských zadání',
            goal='Zkontrolovat kompletnost, srozumitelnost a strategickou relevanci informací získaných od klienta a vytvořit z nich finální, strukturované a akceschopné shrnutí.',
            backstory=(
                "Jsi precizní datový stratég. Tvojí prací je zajistit, aby každé zadání bylo "
                "křišťálově čisté, bez nejasností a přímo navázané na rozhodovací proces klienta. "
                "Tvoje shrnutí jsou legendární svou přehledností."
            ),
            verbose=True,
            allow_delegation=False,
        )
        print("✅ Agent BriefAnalyst vytvořen")
        
    except Exception as e:
        print(f"❌ Chyba při vytváření agentů: {e}")
        return False
    
    # Test definice úkolů
    try:
        task_interview = Task(
            description=(
                "Veď simulovaný rozhovor s klientem, jehož prvotní požadavek je: '{initial_request}'. "
                "Začni obecnou otázkou na hlavní cíl projektu. Na základě fiktivní odpovědi se postupně doptej "
                "na klíčové strategické rozhodnutí, které potřebuje učinit. "
                "Celý přepis tohoto fiktivního dialogu bude tvým finálním výstupem."
            ),
            expected_output='Kompletní přepis fiktivního, ale realistického dialogu mezi tebou a klientem, který pokrývá minimálně cíl projektu a klíčové rozhodnutí.',
            agent=client_onboarder
        )
        print("✅ Task interview vytvořen")
        
        task_analysis_and_summary = Task(
            description=(
                "Vezmi přepis dialogu z předchozího úkolu. Důkladně ho zanalyzuj. "
                "Identifikuj a extrahuj klíčové informace: 'projekt_cil' a 'klicove_rozhodnuti'."
            ),
            expected_output='Strukturovaný výstup ve formátu JSON, který obsahuje klíče "projekt_cil" a "klicove_rozhodnuti" s extrahovanými informacemi. Příklad: {{"projekt_cil": "Zjistit...", "klicove_rozhodnuti": "Rozhodnout o..."}}',
            agent=brief_analyst
        )
        print("✅ Task analysis vytvořen")
        
    except Exception as e:
        print(f"❌ Chyba při vytváření úkolů: {e}")
        return False
    
    # Test sestavení posádky
    try:
        crew = Crew(
            agents=[client_onboarder, brief_analyst],
            tasks=[task_interview, task_analysis_and_summary],
            process=Process.sequential,
            verbose=True,
        )
        print("✅ Crew úspěšně vytvořena")
        
    except Exception as e:
        print(f"❌ Chyba při vytváření Crew: {e}")
        return False
    
    # Ověření struktury
    print(f"📊 Počet agentů: {len(crew.agents)}")
    print(f"📊 Počet úkolů: {len(crew.tasks)}")
    print(f"📊 Proces: {crew.process}")
    
    print("\n🎉 Všechny testy struktury prošly úspěšně!")
    print("📝 Poznámka: Pro plnou funkčnost je potřeba nastavit platný OPENAI_API_KEY v .env souboru")
    
    return True

if __name__ == "__main__":
    test_basic_structure()
