# Konfigurace <PERSON> pro onboarding proces
# Každý úkol odkazuje na agenta pomocí jeho klíče

task_interview_v1:
  description: |
    Veď simulovaný rozhovor s klientem, jeho<PERSON> prvotní požadavek je: '{initial_request}'. 
    Začni obecnou otázkou na hlavní cíl projektu. Na základě fiktivní odpovědi se postupně doptej 
    na klíčové strategické rozhodnutí, které potřebuje učinit. 
    Celý přepis tohoto fiktivního dialogu bude tvým finálním výstupem.
  expected_output: "Kompletní přepis fiktivního, ale realistického dialogu mezi tebou a klientem, který pokrývá minimálně cíl projektu a klíčové rozhodnutí."
  agent: client_onboarder_v1

task_analysis_and_summary_v1:
  description: |
    Vezmi přepis dialogu z předchozího úkolu. Důkladně ho zanalyzuj. 
    Identifikuj a extrahuj klíčové informace: 'projekt_cil' a 'klicove_rozhodnuti'.
  expected_output: 'Strukturovaný výstup ve formátu JSON, který obsahuje klíče "projekt_cil" a "klicove_rozhodnuti" s extrahovanými informacemi. Příklad: {{"projekt_cil": "Zjistit...", "klicove_rozhodnuti": "Rozhodnout o..."}}'
  agent: brief_analyst_v1

# Interaktivní úkoly pro postupný dialog
task_first_question_v1:
  description: |
    Klient přišel s požadavkem: '{initial_request}'. 
    Polož mu jednu cílenou otázku, která pomůže lépe pochopit jeho hlavní cíl projektu. 
    Vrať pouze otázku, bez dalšího textu.
  expected_output: "Jedna konkrétní otázka pro klienta."
  agent: client_onboarder_v1

task_follow_up_question_v1:
  description: |
    Na základě dosavadní konverzace: {conversation_context} 
    a poslední odpovědi klienta: '{last_answer}', 
    polož další cílenou otázku, která pomůže získat více detailů 
    o strategických rozhodnutích, která klient potřebuje učinit. 
    Vrať pouze otázku, bez dalšího textu.
  expected_output: "Jedna konkrétní následující otázka pro klienta."
  agent: client_onboarder_v1

task_final_analysis_v1:
  description: |
    Analyzuj celou konverzaci: {conversation_context} 
    Identifikuj a extrahuj klíčové informace: 'projekt_cil' a 'klicove_rozhodnuti'.
  expected_output: "Strukturovaný výstup ve formátu JSON, který obsahuje klíče \"projekt_cil\" a \"klicove_rozhodnuti\" s extrahovanými informacemi."
  agent: brief_analyst_v1

# Budoucí úkoly pro rozšíření systému
task_survey_design_v1:
  description: |
    Na základě analýzy klientského zadání navrhni kompletní metodologický rámec
    pro výzkumný projekt včetně typu výzkumu, vzorku, metod sběru dat a časového harmonogramu.
    Použij knowledge_base_search k vyhledání relevantních metodologických postupů a best practices.
    Výstup strukturuj jako JSON s klíči: metodologie, vzorek, sber_dat, harmonogram, oduvodneni.
  expected_output: "Detailní metodologický návrh ve strukturovaném JSON formátu s odůvodněním jednotlivých rozhodnutí."
  agent: survey_architect_v1
  enabled: true  # AKTIVNÍ

task_quality_check_v1:
  description: |
    Zkontroluj všechny výstupy předchozích úkolů z hlediska kvality, konzistence
    a úplnosti. Identifikuj případné nedostatky a navrhni zlepšení.
    Použij knowledge_base_search k ověření standardů kvality a best practices.
    Výstup strukturuj jako JSON s klíči: hodnoceni_kvality, identifikovane_problemy, doporuceni_zlepseni, celkove_skore.
  expected_output: "Kvalitativní hodnocení ve strukturovaném JSON formátu s konkrétními doporučeními pro zlepšení."
  agent: quality_supervisor_v1
  enabled: true  # AKTIVNÍ
