#!/usr/bin/env python3
"""
Config Loader pro dynamick<PERSON> na<PERSON> agent<PERSON>, <PERSON><PERSON><PERSON><PERSON> a posádek z YAML souborů
Implementuje Milník 2.2: Dynamická Konfigurace Agentů z Externích Souborů
"""

import os
import yaml
from typing import Dict, List, Any, Optional
from pathlib import Path
from crewai import Agent, Task, Crew, Process
from rag_system import knowledge_base_search, get_rag_system
from best_practices_system import best_practices_search, get_best_practices_system
import logging

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatyldaConfigLoader:
    """Loader pro dynamické načítání konfigurací z YAML souborů"""
    
    def __init__(self, project_root: str = None):
        """
        Inicializace config loaderu
        
        Args:
            project_root: Kořenový adresář projektu
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent
        self.agents_dir = self.project_root / "agents"
        self.tasks_dir = self.project_root / "tasks"
        self.crews_dir = self.project_root / "crews"
        
        # Cache pro načtené konfigurace
        self._agents_cache: Dict[str, Dict] = {}
        self._tasks_cache: Dict[str, Dict] = {}
        self._crews_cache: Dict[str, Dict] = {}
        
        # RAG systém a Best Practices
        self.rag_system = get_rag_system()
        self.best_practices_system = get_best_practices_system()

        # Dostupné nástroje
        self.available_tools = {
            "knowledge_base_search": knowledge_base_search,
            "best_practices_search": best_practices_search
        }
    
    def load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """Načte YAML soubor a vrátí jeho obsah"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = yaml.safe_load(file)
                logger.info(f"✅ Načten YAML soubor: {file_path}")
                return content or {}
        except FileNotFoundError:
            logger.error(f"❌ YAML soubor nenalezen: {file_path}")
            return {}
        except yaml.YAMLError as e:
            logger.error(f"❌ Chyba při parsování YAML: {file_path} - {e}")
            return {}
    
    def load_agents_config(self, config_file: str = "onboarding_agents.yaml") -> Dict[str, Dict]:
        """Načte konfiguraci agentů"""
        if config_file not in self._agents_cache:
            file_path = self.agents_dir / config_file
            self._agents_cache[config_file] = self.load_yaml_file(file_path)
        return self._agents_cache[config_file]
    
    def load_tasks_config(self, config_file: str = "onboarding_tasks.yaml") -> Dict[str, Dict]:
        """Načte konfiguraci úkolů"""
        if config_file not in self._tasks_cache:
            file_path = self.tasks_dir / config_file
            self._tasks_cache[config_file] = self.load_yaml_file(file_path)
        return self._tasks_cache[config_file]
    
    def load_crews_config(self, config_file: str = "onboarding_crew.yaml") -> Dict[str, Dict]:
        """Načte konfiguraci posádek"""
        if config_file not in self._crews_cache:
            file_path = self.crews_dir / config_file
            self._crews_cache[config_file] = self.load_yaml_file(file_path)
        return self._crews_cache[config_file]
    
    def create_agent(self, agent_id: str, config_file: str = "onboarding_agents.yaml") -> Optional[Agent]:
        """
        Vytvoří agenta na základě konfigurace
        
        Args:
            agent_id: ID agenta v konfiguraci
            config_file: Název konfiguračního souboru
            
        Returns:
            Instance Agent nebo None při chybě
        """
        agents_config = self.load_agents_config(config_file)
        
        if agent_id not in agents_config:
            logger.error(f"❌ Agent '{agent_id}' nenalezen v konfiguraci")
            return None
        
        agent_config = agents_config[agent_id]
        
        # Kontrola, zda je agent povolen
        if not agent_config.get('enabled', True):
            logger.warning(f"⚠️ Agent '{agent_id}' je zakázán v konfiguraci")
            return None
        
        try:
            # Příprava nástrojů
            tools = []
            for tool_name in agent_config.get('tools', []):
                if tool_name in self.available_tools:
                    # Kontrola dostupnosti systémů
                    if tool_name == "knowledge_base_search" and self.rag_system.is_available():
                        tools.append(self.available_tools[tool_name])
                    elif tool_name == "best_practices_search":
                        tools.append(self.available_tools[tool_name])
                    elif tool_name not in ["knowledge_base_search", "best_practices_search"]:
                        tools.append(self.available_tools[tool_name])
                else:
                    logger.warning(f"⚠️ Nástroj '{tool_name}' není dostupný")
            
            # Vytvoření agenta
            agent = Agent(
                role=agent_config['role'],
                goal=agent_config['goal'],
                backstory=agent_config['backstory'],
                tools=tools,
                verbose=agent_config.get('verbose', True),
                allow_delegation=agent_config.get('allow_delegation', False)
            )
            
            logger.info(f"✅ Agent '{agent_id}' vytvořen s {len(tools)} nástroji")
            return agent
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření agenta '{agent_id}': {e}")
            return None
    
    def create_task(self, task_id: str, agents: Dict[str, Agent], 
                   config_file: str = "onboarding_tasks.yaml", **kwargs) -> Optional[Task]:
        """
        Vytvoří úkol na základě konfigurace
        
        Args:
            task_id: ID úkolu v konfiguraci
            agents: Slovník dostupných agentů
            config_file: Název konfiguračního souboru
            **kwargs: Dodatečné parametry pro formátování
            
        Returns:
            Instance Task nebo None při chybě
        """
        tasks_config = self.load_tasks_config(config_file)
        
        if task_id not in tasks_config:
            logger.error(f"❌ Úkol '{task_id}' nenalezen v konfiguraci")
            return None
        
        task_config = tasks_config[task_id]
        
        # Kontrola, zda je úkol povolen
        if not task_config.get('enabled', True):
            logger.warning(f"⚠️ Úkol '{task_id}' je zakázán v konfiguraci")
            return None
        
        # Najití agenta pro úkol
        agent_id = task_config['agent']
        if agent_id not in agents:
            logger.error(f"❌ Agent '{agent_id}' pro úkol '{task_id}' není dostupný")
            return None
        
        try:
            # Formátování description a expected_output s kwargs
            description = task_config['description'].format(**kwargs)
            expected_output = task_config['expected_output'].format(**kwargs)
            
            # Vytvoření úkolu
            task = Task(
                description=description,
                expected_output=expected_output,
                agent=agents[agent_id]
            )
            
            logger.info(f"✅ Úkol '{task_id}' vytvořen pro agenta '{agent_id}'")
            return task
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření úkolu '{task_id}': {e}")
            return None
    
    def create_crew(self, crew_id: str, config_file: str = "onboarding_crew.yaml", **kwargs) -> Optional[Crew]:
        """
        Vytvoří posádku na základě konfigurace
        
        Args:
            crew_id: ID posádky v konfiguraci
            config_file: Název konfiguračního souboru
            **kwargs: Dodatečné parametry pro úkoly
            
        Returns:
            Instance Crew nebo None při chybě
        """
        crews_config = self.load_crews_config(config_file)
        
        if crew_id not in crews_config:
            logger.error(f"❌ Posádka '{crew_id}' nenalezena v konfiguraci")
            return None
        
        crew_config = crews_config[crew_id]
        
        # Kontrola, zda je posádka povolena
        if not crew_config.get('enabled', True):
            logger.warning(f"⚠️ Posádka '{crew_id}' je zakázána v konfiguraci")
            return None
        
        try:
            # Vytvoření agentů
            agents = {}
            for agent_id in crew_config['agents']:
                agent = self.create_agent(agent_id)
                if agent:
                    agents[agent_id] = agent
                else:
                    logger.error(f"❌ Nepodařilo se vytvořit agenta '{agent_id}' pro posádku '{crew_id}'")
                    return None
            
            # Vytvoření úkolů
            tasks = []
            for task_id in crew_config['tasks']:
                task = self.create_task(task_id, agents, **kwargs)
                if task:
                    tasks.append(task)
                else:
                    logger.error(f"❌ Nepodařilo se vytvořit úkol '{task_id}' pro posádku '{crew_id}'")
                    return None
            
            # Určení procesu
            process_name = crew_config.get('process', 'sequential')
            process = Process.sequential if process_name == 'sequential' else Process.hierarchical
            
            # Vytvoření posádky
            crew = Crew(
                agents=list(agents.values()),
                tasks=tasks,
                process=process,
                verbose=crew_config.get('verbose', True)
            )
            
            logger.info(f"✅ Posádka '{crew_id}' vytvořena s {len(agents)} agenty a {len(tasks)} úkoly")
            return crew
            
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření posádky '{crew_id}': {e}")
            return None
    
    def list_available_configs(self) -> Dict[str, List[str]]:
        """Vrátí seznam dostupných konfigurací"""
        result = {
            "agents": [],
            "tasks": [],
            "crews": []
        }
        
        # Agenti
        agents_config = self.load_agents_config()
        result["agents"] = [agent_id for agent_id, config in agents_config.items() 
                          if config.get('enabled', True)]
        
        # Úkoly
        tasks_config = self.load_tasks_config()
        result["tasks"] = [task_id for task_id, config in tasks_config.items() 
                         if config.get('enabled', True)]
        
        # Posádky
        crews_config = self.load_crews_config()
        result["crews"] = [crew_id for crew_id, config in crews_config.items() 
                         if config.get('enabled', True)]
        
        return result

# Globální instance config loaderu
config_loader = MatyldaConfigLoader()

def get_config_loader() -> MatyldaConfigLoader:
    """Vrátí globální instanci config loaderu"""
    return config_loader

if __name__ == "__main__":
    # Test config loaderu
    print("🧪 Testování Config Loaderu...")
    
    loader = MatyldaConfigLoader()
    
    # Test načítání konfigurací
    print("\n📋 Dostupné konfigurace:")
    configs = loader.list_available_configs()
    for config_type, items in configs.items():
        print(f"  {config_type}: {items}")
    
    # Test vytvoření agenta
    print("\n🤖 Test vytvoření agenta:")
    agent = loader.create_agent("client_onboarder_v1")
    if agent:
        print(f"✅ Agent vytvořen: {agent.role}")
    
    # Test vytvoření posádky
    print("\n👥 Test vytvoření posádky:")
    crew = loader.create_crew("onboarding_crew_basic", initial_request="Test požadavek")
    if crew:
        print(f"✅ Posádka vytvořena s {len(crew.agents)} agenty a {len(crew.tasks)} úkoly")
