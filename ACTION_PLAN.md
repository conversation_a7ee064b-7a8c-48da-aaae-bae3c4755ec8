# AKČNÍ PLÁN: PROJEKT "MATYLDA"

Tento dokument popisuje klíčové milníky a kroky pro vývoj strategického AI partnera Matylda. Detailní vize a architektura jsou popsány v souborech `VISION.md` a `ARCHITECTURE.md`.

---

### **FÁZE 1: Základní Infrastruktura a První Posádka (Crew)**

**Cíl:** Vytvořit funkční jádro aplikace pomocí CrewAI, které zvládne základní onboarding klienta pro výzkumný projekt.

#### **✅ Milník 1.1: Příprava Prostředí a <PERSON>cí <PERSON>ód (DOKONČENO)**
*   **Úkol:** Vytvořit základní strukturu projektu, `main.py` s definicí první posádky, `README.md` a soubor pro závislosti.
*   **Stav:** **DOKONČENO.** Základní infrastruktura projektu je připravena.

#### **✅ Milník 1.2: Implementace a Testování Základní Posádky (DOKONČENO)**
*   **Úkol:** Vzít poskytnutý kód a soubory, nainstalovat závislosti a spustit první test. Cílem je ověřit, že posádka s agenty `ClientOnboarder` a `BriefAnalyst` dokáže na základě jednoduchého vstupu autonomně vygenerovat strukturovaný JSON souhrn.
*   **Stav:** **DOKONČENO.** Posádka funguje správně a generuje strukturované JSON výstupy.
*   **Vstup:** `"Potřebuji udělat průzkum k rekonstrukci dětského hřiště."`
*   **Výstup:** Strukturovaný JSON se shrnutím zadání.

#### **✅ Milník 1.3: Interaktivita a API (DOKONČENO)**
*   **Úkol:** Upravit aplikaci tak, aby neproběhla celá najednou, ale vedla interaktivní dialog. Vytvořit FastAPI endpoint pro komunikaci s frontendem.
*   **Stav:** **DOKONČENO.** Implementován interaktivní dialog (`interactive_main.py`) a FastAPI server (`api_server.py`) s kompletním REST API.

---
### **FÁZE 2: Rozšíření a Vylepšení (Budoucnost)**
**Cíl:** Přeměnit stávající prototyp na robustní, daty řízenou a konfigurovatelnou aplikaci. Připravit systém na budoucí rozšíření o další agenty a znalostní domény.

#### **✅ Milník 2.1: Implementace Znalostní Báze (RAG přes Supabase) (DOKONČENO)**

*   **Cíl:** Vybavit agenty schopností čerpat znalosti z naší centrální databáze v Supabase. Agent `ClientOnboarder` musí být schopen odpovídat na odborné dotazy klienta.
*   **Stav:** **DOKONČENO** - Implementován kompletní RAG systém
*   **Realizované komponenty:**
    *   ✅ `rag_system.py` - Kompletní RAG systém s Supabase Vector Store
    *   ✅ `KnowledgeBaseSearchTool` - CrewAI nástroj pro vyhledávání v znalostní bázi
    *   ✅ Integrace do agentů - Agent `ClientOnboarder` má přístup k nástroji `knowledge_base_search`
    *   ✅ Automatická detekce dostupnosti - RAG systém se aktivuje pouze při správné konfiguraci
*   **Detailní zadání pro Kódéra:**
    1.  **Integrace Supabase Klienta:**
        *   Do `main.py` přidej kód pro inicializaci klienta pro Supabase a pro OpenAI Embeddings. Využij k tomu proměnné `SUPABASE_URL`, `SUPABASE_KEY` a `OPENAI_API_KEY` ze souboru `.env`.
        *   Vytvoř objekt `SupabaseVectorStore` a z něj `retriever` objekt (`vector_store.as_retriever()`), který bude sloužit k prohledávání databáze. Ujisti se, že název tabulky (`gdanalyst`) a funkce (`match_gdanalyst`) odpovídají naší existující RAG databázi.
    2.  **Vytvoření RAG Nástroje:**
        *   Implementuj funkci `knowledge_base_search(query: str)` jako nástroj (`@tool`) pro CrewAI.
        *   Tato funkce přijme textový `query` jako vstup.
        *   Uvnitř funkce použij připravený `retriever` objekt k prohledání Supabase (`retriever.invoke(query)`).
        *   Výsledek (seznam dokumentů) zformátuj do jednoho přehledného textového řetězce (jednotlivé dokumenty odděl např. `---`).
        *   Funkce vrátí tento zformátovaný text.
    3.  **Přiřazení Nástroje Agentovi:**
        *   Při definici agenta `ClientOnboarder` mu do parametru `tools` přidej nově vytvořený `knowledge_base_search` nástroj.
    4.  **Aktualizace Promptu Agenta:**
        *   Mírně uprav `goal` nebo `backstory` pro agenta `ClientOnboarder`, aby explicitně věděl, že má tento nástroj k dispozici a má ho aktivně používat, když se ho klient zeptá na odborný termín nebo si není jistý. (Např. přidej větu: "Když klient potřebuje vysvětlit odborný koncept, použij svůj nástroj `knowledge_base_search`.")

#### **✅ Milník 2.2: Dynamická Konfigurace Agentů z Externích Souborů (DOKONČENO)**

*   **Cíl:** Odstranit "natvrdo" napsané definice agentů a úkolů z `main.py`. Místo toho je načítat z externích, snadno editovatelných `.yaml` souborů. Tím připravíme systém na snadné přidávání nových agentů a "posádek" v budoucnu.
*   **Stav:** **DOKONČENO** - Implementován kompletní config loader systém
*   **Realizované komponenty:**
    *   ✅ Adresářová struktura: `/agents`, `/tasks`, `/crews`
    *   ✅ `agents/onboarding_agents.yaml` - Konfigurace agentů s rolemi, cíli a nástroji
    *   ✅ `tasks/onboarding_tasks.yaml` - Konfigurace úkolů s referencemi na agenty
    *   ✅ `crews/onboarding_crew.yaml` - Konfigurace posádek s různými scénáři
    *   ✅ `config_loader.py` - Dynamický loader pro vytváření agentů, úkolů a posádek z YAML
    *   ✅ `main_dynamic.py` - Nová verze main.py používající config loader
    *   ✅ Podpora pro budoucí rozšíření - připravené konfigurace pro `survey_architect` a `quality_supervisor`
*   **Detailní zadání pro Kódéra:**
    1.  **Vytvoření Adresářové Struktury:**
        *   Vytvoř v projektu adresáře: `/agents`, `/tasks`, `/crews`.
    2.  **Vytvoření Konfiguračních Souborů:**
        *   Vytvoř soubor `/agents/onboarding_agents.yaml`. Zde přesuň definice pro `ClientOnboarder` a `BriefAnalyst` (jejich `role`, `goal`, `backstory`). Každý agent bude mít svůj unikátní klíč (např. `client_onboarder_v1`).
        *   Vytvoř soubor `/tasks/onboarding_tasks.yaml`. Zde přesuň definice pro `task_interview` a `task_analysis_and_summary` (jejich `description` a `expected_output`). Každý úkol bude odkazovat na agenta, který ho má vykonat, pomocí jeho klíče (např. `agent: client_onboarder_v1`).
        *   Vytvoř soubor `/crews/onboarding_crew.yaml`. Zde bude definice naší posádky, která bude odkazovat na seznam agentů a úkolů, které má použít.
    3.  **Implementace Načítací Logiky:**
        *   Do `main.py` (nebo do samostatného modulu `config_loader.py`) přidej funkci, která umí načíst a parsovat tyto `.yaml` soubory.
        *   Uprav `main.py` tak, aby při startu nenačítal agenty a úkoly z kódu, ale dynamicky je vytvářel na základě načtených konfigurací.
        *   Výsledkem by mělo být, že `main.py` bude obsahovat pouze obecnou logiku pro spuštění `Crew`, zatímco veškerá specifika budou v `.yaml` souborech.


*   **Milník 2.3: Rozšíření Týmu:** Přidání agentů `SurveyArchitect` a `QualitySupervisor`.
*   
Detailní Zadání: Milník 2.3 - Rozšíření Týmu o Nové Specialisty
Cíl: Rozšířit naši stávající "posádku" o dva nové, vysoce specializované agenty: SurveyArchitect a QualitySupervisor. Tím transformujeme jednoduchý onboarding proces na komplexnější workflow, které zahrnuje návrh řešení a interní kontrolu kvality.
Stav: K IMPLEMENTACI
Role: Vy (Stratég & Kódér) s podporou AI Kódéra.
Předpoklady: Systém již umí dynamicky načítat agenty, úkoly a posádky z .yaml souborů z adresářů /agents, /tasks a /crews.
Část 1: Definice Nových Agentů
Úkol: Vytvořit nový konfigurační soubor /agents/specialist_agents.yaml a přidat do něj definice pro následující dva agenty.
Agent 1: SurveyArchitect (Architekt Průzkumů)
YAML Soubor: /agents/specialist_agents.yaml
Klíč Agenta: survey_architect_v1
Detailní Konfigurace:
Generated yaml
survey_architect_v1:
  role: 'Expert na Metodologii Výzkumu a Design Dotazníků'
  goal: >
    Na základě strukturovaného zadání (briefu) od BriefAnalyst agenta navrhnout 
    kompletní, metodologicky správnou a efektivní strukturu dotazníku. 
    Tvým cílem je vytvořit návrh, který je připravený pro implementaci v LimeSurvey.
  backstory: >
    Jsi puntičkářský metodolog s hlubokými znalostmi psychometrie, designu otázek 
    a technických specifikací průzkumných platforem jako je LimeSurvey. 
    Rozumíš, jak formulace otázky ovlivňuje kvalitu dat a jak struktura dotazníku 
    působí na respondenta. Tvým úkolem je přeměnit strategické cíle klienta 
    na sadu konkrétních, měřitelných a nezaujatých otázek.
  tools:
    - knowledge_base_search # Potřebuje přístup k bázi pro specifika typů otázek a metodologií
  allow_delegation: false
  verbose: true
Use code with caution.
Yaml
Agent 2: QualitySupervisor (Supervizor Kvality)
YAML Soubor: /agents/specialist_agents.yaml
Klíč Agenta: quality_supervisor_v1
Detailní Konfigurace:
Generated yaml
quality_supervisor_v1:
  role: 'Interní Auditor a Kontrolor Kvality Výstupů'
  goal: >
    Provést kritickou revizi výstupů ostatních agentů (zejména BriefAnalyst a SurveyArchitect),
    identifikovat potenciální slabiny, nejasnosti nebo metodologické chyby a poskytnout 
    konstruktivní zpětnou vazbu pro jejich vylepšení.
  backstory: >
    Jsi ztělesněním principu "ďáblova advokáta" v našem Modelu Geniálního Konzultanta.
    Tvojí prací není tvorba, ale kritické myšlení. Máš nulovou toleranci k nejasným
    formulacím, metodologickým zkratkám a výstupům, které plně neodpovídají 
    původnímu cíli klienta. Jsi poslední a nejdůležitější kontrolní bod předtím,
    než výstup opustí naši "posádku".
  tools:
    - knowledge_base_search # Potřebuje přístup k bázi, aby mohl ověřovat postupy proti best practices
  allow_delegation: true # Může delegovat úkol zpět agentovi s pokynem k přepracování
  verbose: true
Use code with caution.
Yaml
Část 2: Definice Nových Úkolů
Úkol: Vytvořit nový konfigurační soubor /tasks/specialist_tasks.yaml s definicemi úkolů pro nové agenty.
Generated yaml
# Soubor: /tasks/specialist_tasks.yaml

task_design_survey_draft:
  description: >
    Vezmi finální JSON brief, který vytvořil BriefAnalyst.
    Na základě tohoto zadání navrhni detailní strukturu dotazníku.
    Tvůj výstup musí být textový dokument, který obsahuje:
    1. Úvodní text pro respondenty.
    2. Jednotlivé skupiny otázek (např. Demografie, Klíčová Témata).
    3. Pro každou otázku uveď její přesné znění, typ (např. Jedna volba, Matice, Otevřená) a navrhované odpovědi.
    Využij svou znalostní bázi pro výběr nejvhodnějších typů otázek.
  expected_output: >
    Strukturovaný textový dokument obsahující kompletní návrh dotazníku,
    připravený ke kritické revizi.
  agent: survey_architect_v1

task_review_brief_quality:
  description: >
    Analyzuj výstup od BriefAnalyst agenta (JSON shrnutí).
    Zhodnoť, zda jsou všechny klíčové informace (projekt_cil, klicove_rozhodnuti, atd.)
    dostatečně konkrétní, měřitelné a akceschopné. Použij znalostní bázi k porovnání
    s best practices pro definici cílů.
  expected_output: >
    Stručné zhodnocení kvality briefu. Pokud je vše v pořádku, vrať "Brief schválen.".
    Pokud najdeš nedostatky, vrať jasný a stručný seznam bodů, které je potřeba 
    doplnit nebo přeformulovat, a deleguj úkol zpět.
  agent: quality_supervisor_v1
  context:
    - task_analysis_and_summary # Tento úkol závisí na výstupu z úkolu shrnutí

task_review_survey_draft:
  description: >
    Analyzuj návrh dotazníku od SurveyArchitect agenta.
    Zaměř se na:
    1. Soulad otázek s původním cílem projektu (z briefu).
    2. Metodologickou správnost (vyhýbání se sugestivním otázkám, správné použití typů otázek).
    3. Srozumitelnost pro cílovou skupinu respondentů.
  expected_output: >
    Seznam konkrétních připomínek a doporučení k vylepšení návrhu dotazníku.
    Pokud je návrh perfektní, vrať "Návrh dotazníku schválen bez připomínek.".
  agent: quality_supervisor_v1
  context:
    - task_design_survey_draft # Tento úkol závisí na výstupu z úkolu návrhu dotazníku
Use code with caution.
Yaml
Část 3: Sestavení Nové, Rozšířené Posádky (Crew)
Úkol: Vytvořit nový soubor /crews/full_onboarding_crew.yaml, který bude orchestrovat celý proces od A do Z.
Generated yaml
# Soubor: /crews/full_onboarding_crew.yaml

# Seznam klíčů agentů, kteří jsou součástí této posádky
agents:
  - client_onboarder_v1
  - brief_analyst_v1
  - quality_supervisor_v1 # Přidáváme Supervizora
  - survey_architect_v1   # Přidáváme Architekta

# Seznam klíčů úkolů, které se mají vykonat
tasks:
  - task_interview
  - task_analysis_and_summary
  - task_review_brief_quality   # Nový kontrolní úkol
  - task_design_survey_draft    # Nový úkol pro návrh
  - task_review_survey_draft    # Finální kontrola

# Způsob, jakým se úkoly vykonávají
process: sequential # Stále postupně, jeden po druhém

# Paměť pro sdílení kontextu mezi úkoly
memory: true

*   **Milník 2.4: Učení ze Zkušenosti:** Implementace ukládání a využívání `best_practices`.
Detailní Zadání: Milník 2.4 - Učení ze Zkušenosti (Best Practices)
Cíl: Vytvořit mechanismus, pomocí kterého systém (konkrétně QualitySupervisor) analyzuje dokončené projekty, extrahuje z nich úspěšné postupy a ukládá je jako "best practices" do specializované znalostní báze. Tito "best practices" pak budou využíváni ostatními agenty k vylepšení jejich budoucích rozhodnutí.
Stav: K IMPLEMENTACI
Role: Vy (Stratég & Kódér) s podporou AI Kódéra.
Předpoklady: Fáze 2.3 je hotová. Máme funkční posádku, která umí provést celý onboarding a návrh.
Část 1: Příprava Paměti na Zkušenosti (Supabase)
Úkol: Vytvořit novou tabulku v Supabase, která bude sloužit jako naše "knihovna ověřených postupů".
Název tabulky: best_practices
Sloupce:
id: uuid (Primary Key, generovaný automaticky)
created_at: timestamp (čas vytvoření)
context_description: text (Stručný popis situace nebo problému, např. "Klient se nemůže rozhodnout mezi dvěma investičními variantami.")
successful_strategy: text (Popis postupu, který vedl k úspěšnému řešení, např. "Navrhnout IPA matici a vysvětlit její kvadranty na příkladu z dopravy.")
associated_agent_role: text (Role agenta, kterého se tento postup týká, např. "ClientOnboarder")
success_rating: float4 (Hodnocení úspěšnosti, např. 0.95)
feedback_notes: text[] (Pole textů s dodatečnými poznámkami, např. {"Klient ocenil vizuální návrh.", "Příště se více zaměřit na sběr dat pro osu 'důležitost'."})
embedding: vector (Vektorová reprezentace sloupce context_description pro sémantické vyhledávání)
Poznámka pro Kódéra: Bude potřeba vytvořit a nastavit RLS (Row Level Security) a databázovou funkci (např. match_best_practices) pro sémantické vyhledávání v této nové tabulce.
Část 2: Vylepšení QualitySupervisor Agenta
Úkol: Povýšit QualitySupervisor z pouhého "kritika" na "mentora a historika". Musí dostat nový úkol a nové nástroje.
Nový Nástroj 1: save_best_practice
Implementace: Vytvořit nový nástroj (@tool) v Pythonu.
Vstup: context_description, successful_strategy, associated_agent_role, feedback_notes.
Funkce:
Vezme text z context_description a pomocí OpenAI API pro něj vygeneruje embedding.
Vloží nový záznam do tabulky best_practices v Supabase se všemi poskytnutými daty a novým embeddingem.
Vrátí potvrzení o úspěšném uložení.
Nový Nástroj 2: find_relevant_best_practice
Implementace: Vytvořit nový nástroj (@tool) v Pythonu.
Vstup: current_situation_description (textový popis aktuálního problému).
Funkce:
Vezme text z current_situation_description a vygeneruje pro něj embedding.
Provede sémantické vyhledávání v tabulce best_practices pomocí databázové funkce match_best_practices.
Vrátí 1-2 nejrelevantnější nalezené "best practices" jako formátovaný text.
Aktualizace Konfigurace QualitySupervisor
YAML Soubor: /agents/specialist_agents.yaml
Upravit quality_supervisor_v1:
Do sekce tools přidej nově vytvořené nástroje: save_best_practice a find_relevant_best_practice.
Část 3: Definice Nového "Učícího se" Úkolu
Úkol: Vytvořit nový úkol, který se spustí na konci celého workflow a aktivuje učící se mechanismus.
YAML Soubor: /tasks/specialist_tasks.yaml
Nový Úkol:
Generated yaml
task_learn_from_experience:
  description: >
    Toto je finální úkol celého procesu. Vezmi kompletní historii interakce,
    včetně finálního návrhu dotazníku od SurveyArchitect a všech revizí od QualitySupervisor.
    Tvým úkolem je provést "post-mortem" analýzu.
    1. Zhodnoť celkový úspěch projektu. Byl brief jasný? Byl návrh dotazníku kvalitní?
    2. Identifikuj jeden klíčový moment nebo postup, který vedl k úspěchu.
    3. Zobecni tento úspěšný postup do formy "best practice".
    4. Použij nástroj `save_best_practice` k uložení této nové zkušenosti do naší centrální paměti.
  expected_output: >
    Stručné potvrzení o provedení analýzy a uložení nového best practice, 
    včetně ID nově vytvořeného záznamu. Např: "Analýza dokončena. Nová zkušenost 
    'Jak řešit nerozhodnost klienta mezi dvěma variantami' uložena pod ID 12345."
  agent: quality_supervisor_v1
  context:
    # Tento úkol závisí na všech předchozích úkolech!
    - task_interview
    - task_analysis_and_summary
    - task_review_brief_quality
    - task_design_survey_draft
    - task_review_survey_draft
Use code with caution.
Yaml
Část 4: Vylepšení Ostatních Agentů
Úkol: Upravit agenty ClientOnboarder a SurveyArchitect tak, aby využívali nové zkušenosti.
YAML Soubor: /agents/onboarding_agents.yaml a /agents/specialist_agents.yaml
Upravit backstory a goal těchto agentů:
Přidejte instrukci: "Předtím, než se rozhodneš, jaký bude tvůj další krok, zvaž použití nástroje find_relevant_best_practice a popiš mu aktuální situaci. Získané doporučení zohledni ve svém rozhodování."
Přiřazení nástroje: Přidejte nástroj find_relevant_best_practice do tools listu pro ClientOnboarder a SurveyArchitect.
Tímto je cyklus uzavřen. Matylda se nejen učí z každého projektu, ale své nabyté zkušenosti i aktivně využívá v budoucích interakcích. To je definice systému, který se stává moudřejším, nikoli jen chytřejším.