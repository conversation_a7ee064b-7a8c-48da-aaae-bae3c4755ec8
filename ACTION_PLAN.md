# AKČNÍ PLÁN: PROJEKT "MATYLDA"

Tento dokument popisuje klíčové milníky a kroky pro vývoj strategického AI partnera Matylda. Detailní vize a architektura jsou popsány v souborech `VISION.md` a `ARCHITECTURE.md`.

---

### **FÁZE 1: Základní Infrastruktura a První Posádka (Crew)**

**Cíl:** Vytvořit funkční jádro aplikace pomocí CrewAI, které zvládne základní onboarding klienta pro výzkumný projekt.

#### **✅ Milník 1.1: Příprava Prostředí a Startovací Kód (Hotovo - viz níže)**
*   **Úkol:** Vytvořit základní strukturu projektu, `main.py` s definicí první posádky, `README.md` a soubor pro závislosti.
*   **Stav:** **HOTOVO.** Tento úkol byl realizován AI Architektem. Následující soubory jsou připraveny k implementaci.

#### **Milník 1.2: Implementace a Testování Základní Posádky (Váš další úkol)**
*   **Úkol:** Vzít poskytnutý kód a soubory, nainstalovat závislosti a spustit první test. Cílem je ověřit, že posádka s agenty `ClientOnboarder` a `BriefAnalyst` dokáže na základě jednoduchého vstupu autonomně vygenerovat strukturovaný JSON souhrn.
*   **Role:** Vy (Stratég & Kódér) s podporou AI Kódéra.
*   **Vstup:** `"Potřebuji udělat průzkum k rekonstrukci dětského hřiště."`
*   **Očekávaný výstup:** Strukturovaný JSON se shrnutím zadání.

#### **Milník 1.3: Interaktivita a API (Budoucí úkol)**
*   **Úkol:** Upravit aplikaci tak, aby neproběhla celá najednou, ale vedla interaktivní dialog. Vytvořit FastAPI endpoint pro komunikaci s frontendem.
*   **Role:** Vy (Stratég & Kódér) s podporou AI Kódéra, Já (AI Architekt) pro návrh logiky.

---
### **FÁZE 2: Rozšíření a Vylepšení (Budoucnost)**
**Cíl:** Přeměnit stávající prototyp na robustní, daty řízenou a konfigurovatelnou aplikaci. Připravit systém na budoucí rozšíření o další agenty a znalostní domény.

#### **Milník 2.1: Implementace Znalostní Báze (RAG přes Supabase)**

*   **Cíl:** Vybavit agenty schopností čerpat znalosti z naší centrální databáze v Supabase. Agent `ClientOnboarder` musí být schopen odpovídat na odborné dotazy klienta.
*   **Stav:** **K IMPLEMENTACI**
*   **Role:** Vy (Stratég & Kódér) s podporou AI Kódéra.
*   **Detailní zadání pro Kódéra:**
    1.  **Integrace Supabase Klienta:**
        *   Do `main.py` přidej kód pro inicializaci klienta pro Supabase a pro OpenAI Embeddings. Využij k tomu proměnné `SUPABASE_URL`, `SUPABASE_KEY` a `OPENAI_API_KEY` ze souboru `.env`.
        *   Vytvoř objekt `SupabaseVectorStore` a z něj `retriever` objekt (`vector_store.as_retriever()`), který bude sloužit k prohledávání databáze. Ujisti se, že název tabulky (`gdanalyst`) a funkce (`match_gdanalyst`) odpovídají naší existující RAG databázi.
    2.  **Vytvoření RAG Nástroje:**
        *   Implementuj funkci `knowledge_base_search(query: str)` jako nástroj (`@tool`) pro CrewAI.
        *   Tato funkce přijme textový `query` jako vstup.
        *   Uvnitř funkce použij připravený `retriever` objekt k prohledání Supabase (`retriever.invoke(query)`).
        *   Výsledek (seznam dokumentů) zformátuj do jednoho přehledného textového řetězce (jednotlivé dokumenty odděl např. `---`).
        *   Funkce vrátí tento zformátovaný text.
    3.  **Přiřazení Nástroje Agentovi:**
        *   Při definici agenta `ClientOnboarder` mu do parametru `tools` přidej nově vytvořený `knowledge_base_search` nástroj.
    4.  **Aktualizace Promptu Agenta:**
        *   Mírně uprav `goal` nebo `backstory` pro agenta `ClientOnboarder`, aby explicitně věděl, že má tento nástroj k dispozici a má ho aktivně používat, když se ho klient zeptá na odborný termín nebo si není jistý. (Např. přidej větu: "Když klient potřebuje vysvětlit odborný koncept, použij svůj nástroj `knowledge_base_search`.")

#### **Milník 2.2: Dynamická Konfigurace Agentů z Externích Souborů**

*   **Cíl:** Odstranit "natvrdo" napsané definice agentů a úkolů z `main.py`. Místo toho je načítat z externích, snadno editovatelných `.yaml` souborů. Tím připravíme systém na snadné přidávání nových agentů a "posádek" v budoucnu.
*   **Stav:** **K IMPLEMENTACI**
*   **Role:** Vy (Stratég & Kódér) s podporou AI Kódéra.
*   **Detailní zadání pro Kódéra:**
    1.  **Vytvoření Adresářové Struktury:**
        *   Vytvoř v projektu adresáře: `/agents`, `/tasks`, `/crews`.
    2.  **Vytvoření Konfiguračních Souborů:**
        *   Vytvoř soubor `/agents/onboarding_agents.yaml`. Zde přesuň definice pro `ClientOnboarder` a `BriefAnalyst` (jejich `role`, `goal`, `backstory`). Každý agent bude mít svůj unikátní klíč (např. `client_onboarder_v1`).
        *   Vytvoř soubor `/tasks/onboarding_tasks.yaml`. Zde přesuň definice pro `task_interview` a `task_analysis_and_summary` (jejich `description` a `expected_output`). Každý úkol bude odkazovat na agenta, který ho má vykonat, pomocí jeho klíče (např. `agent: client_onboarder_v1`).
        *   Vytvoř soubor `/crews/onboarding_crew.yaml`. Zde bude definice naší posádky, která bude odkazovat na seznam agentů a úkolů, které má použít.
    3.  **Implementace Načítací Logiky:**
        *   Do `main.py` (nebo do samostatného modulu `config_loader.py`) přidej funkci, která umí načíst a parsovat tyto `.yaml` soubory.
        *   Uprav `main.py` tak, aby při startu nenačítal agenty a úkoly z kódu, ale dynamicky je vytvářel na základě načtených konfigurací.
        *   Výsledkem by mělo být, že `main.py` bude obsahovat pouze obecnou logiku pro spuštění `Crew`, zatímco veškerá specifika budou v `.yaml` souborech.


*   **Milník 2.0: Rozšíření Týmu:** Přidání agentů `SurveyArchitect` a `QualitySupervisor`.
*   **Milník 2.2: Dynamická Konfigurace:** Implementace načítání agentů z externích `.yaml` souborů.
*   **Milník 2.3: Učení ze Zkušenosti:** Implementace ukládání a využívání `best_practices`.